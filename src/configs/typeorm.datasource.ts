import { DataSource } from 'typeorm';
import { config as dotenvConfig } from 'dotenv';

dotenvConfig({ path: '.env' });

export const dataSource = new DataSource({
  type: 'mssql',
  host: process.env.DATABASE_HOST,
  port: +process.env.DATABASE_PORT,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  schema: process.env.DATABASE_SCHEMA,
  synchronize: false,
  options: { encrypt: true },
  entities: ['./src/entities/*.{ts,js}'],
  migrations: ['./src/database/migrations/*.{ts,js}'],
  requestTimeout: 30000,
  connectionTimeout: 30000,
  migrationsTableName: 'northstar_domain_migrations'
});
