import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { TargetEntity } from './targets.entity';
import { TargetType } from './../enums';

@Entity({ name: 'target_types' })
export class TargetTypeEntity extends BaseEntity {
  @Column({
    name: 'uidTarget',
    type: 'uniqueidentifier'
  })
  uidTarget: string;

  @Column({
    type: 'nvarchar',
    nullable: false
  })
  type: TargetType;

  @Column({
    type: 'bit',
    nullable: true
  })
  agree?: boolean;

  @ManyToOne(() => TargetEntity, (target) => target.targetTypes)
  @JoinColumn({ name: 'uidTarget' })
  target: TargetEntity;
}
