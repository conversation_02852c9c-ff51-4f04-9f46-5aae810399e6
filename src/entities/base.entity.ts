import { Column, CreateDateColumn, DeleteDateColumn, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseEntity {
  @PrimaryColumn({ type: 'uniqueidentifier', default: () => 'NEWID()' })
  uid: string;

  @DeleteDateColumn({ name: 'deletedAt', type: 'datetime2', nullable: true, select: false })
  deletedAt?: Date;

  @CreateDateColumn({ name: 'createdAt', type: 'datetime2', select: false })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updatedAt', type: 'datetime2', nullable: true, select: false })
  updatedAt?: Date;

  @Column({ name: 'deletedBy', type: 'uniqueidentifier', nullable: true, select: false })
  deletedBy?: string;

  @Column({ name: 'createdBy', type: 'uniqueidentifier', nullable: true, select: false })
  createdBy?: string;

  @Column({ name: 'updatedBy', type: 'uniqueidentifier', nullable: true, select: false })
  updatedBy?: string;
}
