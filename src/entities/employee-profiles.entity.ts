import { <PERSON>um<PERSON>, PrimaryC<PERSON>umn, <PERSON>tity, OneToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { RoleEntity } from './role.entity';
import { EmployeeEntity } from './employees.entity';

@Entity({ name: 'employee_profiles' })
export class EmployeeProfilesEntity {
  @PrimaryColumn({ type: 'uniqueidentifier' })
  uid: string;

  @Column({ name: 'cod_profile_id' })
  codProfileId: string;

  @Column({ name: 'cod_abi_id' })
  codAbiId: number;

  @Column({ name: 'str_cod_role_id' })
  strCodRoleId: string;

  @Column({ name: 'str_function' })
  strFunction: string;

  @OneToOne(() => EmployeeEntity, (employee) => employee.employeeProfile)
  @JoinColumn({ name: 'cod_abi_id', referencedColumnName: 'globalId' })
  employee: EmployeeEntity;

  @OneToOne(() => RoleEntity, (role) => role.employeeProfile)
  @JoinColumn({ name: 'str_cod_role_id', referencedColumnName: 'code' })
  role: RoleEntity;
}
