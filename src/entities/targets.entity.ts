import { Entity, Column, ManyToOne, OneToMany, Join<PERSON>olumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { DeliverableEntity } from './deliverables.entity';
import { ProposalEntity } from './proposals.entity';
import { TargetTypeEntity } from './target-types.entity';

@Entity({ name: 'targets' })
export class TargetEntity extends BaseEntity {
  @Column({ type: 'decimal', nullable: true })
  weight?: number;

  @Column({
    type: 'nvarchar',
    length: 255,
    nullable: true
  })
  scope?: string;

  @Column({
    name: 'uidDeliverable',
    type: 'uniqueidentifier',
    nullable: true
  })
  uidDeliverable?: string;

  @Column({
    name: 'uidParentTarget',
    type: 'uniqueidentifier',
    nullable: true
  })
  uidParentTarget?: string;

  @Column({
    name: 'uidProposal',
    type: 'uniqueidentifier',
    nullable: true
  })
  uidProposal?: string;

  @ManyToOne(() => ProposalEntity, (proposal) => proposal.targets, { nullable: true })
  @JoinColumn({ name: 'uidProposal' })
  proposal: ProposalEntity;

  @ManyToOne(() => DeliverableEntity, (deliverable) => deliverable.targets, { nullable: true })
  @JoinColumn({ name: 'uidDeliverable' })
  deliverable: DeliverableEntity;

  @ManyToOne(() => TargetEntity, (target) => target.children, { nullable: true })
  @JoinColumn({ name: 'uidParentTarget' })
  parentTarget: TargetEntity;

  @OneToMany(() => TargetEntity, (target) => target.parentTarget)
  children: TargetEntity[];

  @OneToMany(() => TargetTypeEntity, (targetType) => targetType.target)
  targetTypes: TargetTypeEntity[];
}
