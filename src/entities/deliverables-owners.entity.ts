import { Entity, Column, ManyToMany } from 'typeorm';
import { DeliverableEntity } from './deliverables.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'deliverables_owners' })
export class DeliverableOwnerEntity extends BaseEntity {
  @Column({ name: 'employeeUuid', nullable: true })
  employeeUuid?: string;

  @Column({ name: 'email' })
  email: string;

  @Column({ name: 'globalId' })
  globalId: number;

  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'positionTitle' })
  positionTitle: string;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.owners)
  deliverables: DeliverableEntity[];
}
