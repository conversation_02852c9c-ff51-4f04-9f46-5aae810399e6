import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { ZoneEntity } from './zones.entity';
import { EmployeeProfilesEntity } from './employee-profiles.entity';

@Entity({ name: 'role' })
export class RoleEntity {
  @PrimaryColumn({ name: 'cod_role_id', type: 'nvarchar' })
  code: string;

  @Column({ name: 'str_name' })
  name: string;

  @Column({ name: 'uid_zone', type: 'uniqueidentifier' })
  zoneUid: string;

  @OneToOne(() => ZoneEntity, (zone) => zone.role)
  @JoinColumn({ name: 'uid_zone', referencedColumnName: 'uid' })
  zone: ZoneEntity;

  @OneToOne(() => EmployeeProfilesEntity, (employee) => employee.role)
  employeeProfile: EmployeeProfilesEntity;
}
