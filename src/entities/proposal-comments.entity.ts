import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { ProposalEmployeeEntity } from './proposals-employees.entity';
import { ProposalEntity } from './proposals.entity';

import { Index } from 'typeorm';
import { TargetEntity } from './targets.entity';

@Entity({ name: 'proposal_comments' })
@Index('IDX_proposal_comments_proposalId', ['proposalId'])
@Index('IDX_proposal_comments_createdAt', ['createdAt'])
@Index('IDX_proposal_comments_employeeId', ['employeeId'])
export class ProposalCommentsEntity extends BaseEntity {
  @Column({ name: 'proposalId', type: 'uniqueidentifier' })
  proposalId: string;

  @ManyToOne(() => ProposalEntity, { nullable: false })
  @JoinColumn({ name: 'proposalId' })
  proposal: ProposalEntity;

  @Column({ name: 'targetId', type: 'uniqueidentifier', nullable: true })
  targetId: string | null;

  @ManyToOne(() => TargetEntity, { nullable: true })
  @JoinColumn({ name: 'targetId' })
  target: TargetEntity | null;

  @Column({ type: 'nvarchar', length: 'MAX' })
  message: string;

  @Column({ name: 'employeeId', type: 'uniqueidentifier' })
  employeeId: string;

  @ManyToOne(() => ProposalEmployeeEntity, { nullable: false })
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'employeeUuid' })
  employee: ProposalEmployeeEntity;
}
