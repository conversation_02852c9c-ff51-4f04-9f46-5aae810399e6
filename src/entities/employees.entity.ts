import { <PERSON><PERSON><PERSON>, <PERSON>C<PERSON>umn, <PERSON>umn, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { EmployeeProfilesEntity } from './employee-profiles.entity';

@Entity({ name: 'employees', synchronize: false })
export class EmployeeEntity {
  @PrimaryColumn({ type: 'uniqueidentifier' })
  uuid: string;

  @Column({ name: 'int_employeeglobalid' })
  globalId: number;

  @Column({ name: 'str_email' })
  email: string;

  @Column({ name: 'str_firstname' })
  firstname: string;

  @Column({ name: 'str_lastname' })
  lastname: string;

  @Column({ name: 'str_positiontitle' })
  positionTitle: string;

  @OneToOne(() => EmployeeProfilesEntity, (employeeProfile) => employeeProfile.employee)
  employeeProfile: EmployeeProfilesEntity;

  @OneToOne(() => EmployeeEntity)
  @JoinColumn({ name: 'uid_manager_level_slt', referencedColumnName: 'uuid' })
  managerLevelSlt: EmployeeEntity;
}
