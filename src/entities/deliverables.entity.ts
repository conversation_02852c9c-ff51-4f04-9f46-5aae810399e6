import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, JoinTable, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { DeliverableOwnerEntity } from './deliverables-owners.entity';
import { DeliverableTypeEntity } from './deliverables-types.entity';
import { TargetEntity } from './targets.entity';

@Entity({ name: 'deliverables' })
export class DeliverableEntity extends BaseEntity {
  @Column({ name: 'buLevelAggregation', type: 'nvarchar', length: 'MAX', nullable: true })
  buLevelAggregation?: string;

  @Column({ name: 'businessFunction', type: 'nvarchar', nullable: true })
  businessFunction?: string;

  @Column({ name: 'calculationMethod', type: 'nvarchar', length: 'MAX', nullable: true })
  calculationMethod?: string;

  @Column({ name: 'content', type: 'nvarchar', length: 'MAX', nullable: true })
  content?: string;

  @Column({ name: 'dataSource', type: 'nvarchar', length: 'MAX', nullable: true })
  dataSource?: string;

  @Column({ name: 'dateEnd', type: 'datetime2', nullable: true })
  dateEnd?: Date;

  @Column({ name: 'dateStart', type: 'datetime2', nullable: true })
  dateStart?: Date;

  @Column({ name: 'definition', type: 'nvarchar', length: 'MAX', nullable: true })
  definition?: string;

  @Column({ name: 'frequency', type: 'nvarchar', nullable: true })
  frequency?: string;

  @Column({ name: 'isActive', type: 'bit', default: 1 })
  isActive: boolean;

  @Column({ name: 'name', type: 'nvarchar', length: 255 })
  name: string;

  @Column({ name: 'paValue', type: 'nvarchar', length: 'MAX', nullable: true })
  paValue?: string;

  @ManyToOne(() => DeliverableTypeEntity, (type) => type.deliverables)
  @JoinColumn({ name: 'deliverableType' })
  deliverableType?: DeliverableTypeEntity;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.deliverables)
  @JoinTable({ name: 'deliverables_deliverables' })
  deliverables?: DeliverableEntity[];

  @ManyToMany(() => DeliverableOwnerEntity, (owner) => owner.deliverables, { eager: true })
  @JoinTable({ name: 'deliverables_deliverables_owners' })
  owners?: DeliverableOwnerEntity[];

  @OneToMany(() => TargetEntity, (target) => target.deliverable)
  targets?: TargetEntity[];
}
