import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { ProposalEmployeeEntity } from './proposals-employees.entity';

import { Index } from 'typeorm';
import { TargetEntity } from './targets.entity';

@Entity({ name: 'target_comments' })
@Index('IDX_target_comments_targetId', ['targetId'])
@Index('IDX_target_comments_createdAt', ['createdAt'])
@Index('IDX_target_comments_employeeId', ['employeeId'])
export class TargetCommentsEntity extends BaseEntity {
  @Column({ name: 'targetId', type: 'uniqueidentifier' })
  targetId: string;

  @ManyToOne(() => TargetEntity, { nullable: false })
  @JoinColumn({ name: 'targetId' })
  target: TargetEntity;

  @Column({ type: 'nvarchar', length: 'MAX' })
  message: string;

  @Column({ name: 'employeeId', type: 'uniqueidentifier' })
  employeeId: string;

  @ManyToOne(() => ProposalEmployeeEntity, { nullable: false })
  @JoinColumn({ name: 'employeeId', referencedColumnName: 'employeeUuid' })
  employee: ProposalEmployeeEntity;
}
