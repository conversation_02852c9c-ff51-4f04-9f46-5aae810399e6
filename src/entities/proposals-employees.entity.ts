import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';
import { ProposalEntity } from './proposals.entity';
import { EmployeeEntity } from './employees.entity';

@Entity({ name: 'proposals_employees' })
export class ProposalEmployeeEntity {
  @PrimaryColumn({ name: 'employeeUuid', type: 'uniqueidentifier' })
  employeeUuid: string;

  @Column({ name: 'globalId' })
  globalId: number;

  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'sltLevel' })
  sltLevel: string;

  @Column({ name: 'zone' })
  zone: string;

  @Column({ name: 'businessFunction' })
  businessFunction: string;

  @ManyToOne(() => EmployeeEntity, { nullable: false })
  @JoinColumn({ name: 'employeeUuid', referencedColumnName: 'uuid' })
  employee?: EmployeeEntity;

  @OneToMany(() => ProposalEntity, (proposal) => proposal.employee)
  proposals?: ProposalEntity[];
}
