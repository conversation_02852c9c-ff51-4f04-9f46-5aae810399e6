import { Column, Entity, OneToOne, PrimaryColumn } from 'typeorm';
import { RoleEntity } from './role.entity';

@Entity({ name: 'tr_zones' })
export class ZoneEntity {
  @PrimaryColumn({ type: 'uniqueidentifier' })
  uid: string;

  @Column({ name: 'str_name' })
  name: string;

  @Column({ name: 'str_lang_code' })
  langCode: string;

  @OneToOne(() => RoleEntity, (role) => role.zone)
  role: RoleEntity;
}
