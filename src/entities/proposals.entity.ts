import { Entity, Column, OneToMany, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { TargetEntity } from './targets.entity';
import { ProposalStatus } from '../enums';
import { ProposalEmployeeEntity } from './proposals-employees.entity';

@Entity({ name: 'proposal' })
export class ProposalEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 50,
    enum: ProposalStatus,
    default: ProposalStatus.NOT_STARTED
  })
  status: ProposalStatus;

  @Column({ name: 'uidEmployee', type: 'uniqueidentifier', nullable: true })
  uidEmployee?: string;

  @Column({ name: 'dateStart', type: 'date', nullable: true })
  dateStart?: Date;

  @Column({ name: 'dateEnd', type: 'date', nullable: true })
  dateEnd?: Date;

  @ManyToOne(() => ProposalEmployeeEntity, (proposalEmployee) => proposalEmployee.proposals)
  @JoinColumn({ name: 'uidEmployee', referencedColumnName: 'employeeUuid' })
  employee: ProposalEmployeeEntity;

  @OneToMany(() => TargetEntity, (target) => target.proposal, { cascade: true })
  targets: TargetEntity[];
}
