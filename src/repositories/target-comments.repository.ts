import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TargetCommentsEntity } from '../entities/target-comments.entity';
import { QueryFailedError } from 'typeorm';
import { BaseRepository } from './base.repository';
import { TargetComments } from 'src/types';

@Injectable()
export class TargetCommentsRepository extends BaseRepository<TargetCommentsEntity> {
  constructor(
    @InjectRepository(TargetCommentsEntity)
    public repository: Repository<TargetCommentsEntity>
  ) {
    super(repository);
  }

  private createCommentQueryBuilder() {
    return this.repository
      .createQueryBuilder('comment')
      .select(['comment.uid', 'comment.message', 'comment.createdAt', 'employee.globalId', 'employee.name'])
      .leftJoin('comment.employee', 'employee');
  }

  private mapEntityToTargetComment(entity: TargetCommentsEntity): TargetComments {
    return {
      id: entity.uid,
      author: {
        globalId: entity.employee?.globalId?.toString() ?? '',
        name: entity.employee?.name ?? ''
      },
      message: entity.message,
      createdAt: entity.createdAt
    };
  }

  async findMessagesWithPagination(
    targetId: string,
    pageNumber: number,
    pageSize: number
  ): Promise<{
    data: TargetComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    const qb = this.createCommentQueryBuilder()
      .where('comment.targetId = :targetId', { targetId })
      .orderBy('comment.createdAt', 'DESC')
      .skip((pageNumber - 1) * pageSize)
      .take(pageSize);

    const [entities, totalRecords] = await qb.getManyAndCount();

    const data: TargetComments[] = entities.map((entity) => this.mapEntityToTargetComment(entity));

    return {
      data,
      pageNumber,
      pageSize,
      totalRecords
    };
  }

  async findCommentByUid(uid: string): Promise<TargetComments | null> {
    const entity = await this.createCommentQueryBuilder().where('comment.uid = :uid', { uid }).getOne();

    return entity ? this.mapEntityToTargetComment(entity) : null;
  }

  async createMessage(targetId: string, message: string, employeeId: string): Promise<TargetComments> {
    try {
      const saved = await this.repository.save({
        targetId,
        message,
        employeeId
      });

      if (!saved || !saved.uid) {
        throw new Error('Failed to create comment');
      }

      const comment = await this.findCommentByUid(saved.uid);

      return comment;
    } catch (err: unknown) {
      const isQueryFailed =
        err instanceof QueryFailedError || (err && typeof err === 'object' && 'name' in err && err.name === 'QueryFailedError');
      if (isQueryFailed && err && typeof err === 'object' && 'message' in err && typeof err.message === 'string') {
        if (err.message.includes('FK_target_comments_targetId') || err.message.includes('targetId')) {
          throw new NotFoundException({ type: 'target_not_found', message: `Target with id ${targetId} does not exist` });
        }
        if (err.message.includes('FK_target_comments_employeeId') || err.message.includes('employeeId')) {
          throw new NotFoundException({ type: 'employee_not_found', message: `Employee with id ${employeeId} does not exist` });
        }
      }

      throw err;
    }
  }

  async deleteMessage(id: string) {
    await this.repository.createQueryBuilder().softDelete().from(TargetCommentsEntity).where('uid = :id', { id }).execute();
  }
}
