import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { TargetEntity } from '../entities/targets.entity';
import { BaseRepository } from './base.repository';
import { TargetTypeRepository } from './target-type.repository';

@Injectable()
export class TargetRepository extends BaseRepository<TargetEntity> {
  constructor(
    @InjectRepository(TargetEntity)
    public repository: Repository<TargetEntity>,
    private readonly targetTypeRepository: TargetTypeRepository
  ) {
    super(repository);
  }

  async createTarget(target: TargetEntity): Promise<TargetEntity> {
    const savedTarget = await this.repository.save(target);
    return savedTarget;
  }

  async createTargets(targets: TargetEntity[]): Promise<TargetEntity[]> {
    const savedTargets = await this.repository.save(targets);
    return savedTargets;
  }

  async findAllTargets(): Promise<TargetEntity[]> {
    return this.repository.find({
      relations: ['parentTarget', 'targetTypes']
    });
  }

  async findByUid(uid: string): Promise<TargetEntity | null> {
    return this.repository
      .createQueryBuilder('target')
      .leftJoinAndSelect('target.children', 'children')
      .leftJoinAndSelect('children.targetTypes', 'childTargetTypes')
      .leftJoinAndSelect('target.targetTypes', 'targetTypes')
      .where('target.uid = :uid', { uid })
      .getOne();
  }

  async findByUids(uids: string[]): Promise<TargetEntity[]> {
    console.log('Finding targets by UIDs:', uids);
    if (uids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('target')
      .leftJoinAndSelect('target.children', 'children')
      .leftJoinAndSelect('children.targetTypes', 'childTargetTypes')
      .leftJoinAndSelect('target.targetTypes', 'targetTypes')
      .where('target.uid IN (:...uids)', { uids })
      .getMany();
  }

  async updateTarget(target: TargetEntity): Promise<TargetEntity> {
    const updatedTarget = await this.repository.findOne({
      where: { uid: target.uid }
    });

    if (!updatedTarget) {
      throw new Error(`Target with UID ${target.uid} not found`);
    }

    Object.assign(updatedTarget, target);
    await this.repository.save(updatedTarget);

    if (updatedTarget.uidParentTarget) {
      const parentTarget = await this.findByUid(updatedTarget.uidParentTarget);

      await this.updateParentTargetWeight(parentTarget);
    }

    return updatedTarget;
  }

  async updateTargetsByCondition(
    updateData: Partial<TargetEntity>,
    whereCondition: string,
    parameters: Record<string, unknown>
  ): Promise<void> {
    await this.repository.createQueryBuilder().update(TargetEntity).set(updateData).where(whereCondition, parameters).execute();
  }

  async updateMultipleTargetsByUids(uids: string[], updateData: Partial<TargetEntity>): Promise<void> {
    if (uids.length === 0) {
      return;
    }

    await this.updateTargetsByCondition(updateData, 'uid IN (:...uids)', { uids });
  }

  async findTargetsByParentUids(parentUids: string[]): Promise<TargetEntity[]> {
    if (parentUids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('target')
      .select(['target.uid'])
      .where('target.uidParentTarget IN (:...parentUids)', { parentUids })
      .getMany();
  }

  async deleteTargetsByUids(uids: string[]): Promise<void> {
    if (uids.length === 0) {
      return;
    }

    const childTargets = await this.findTargetsByParentUids(uids);
    const childTargetUids = childTargets.map((target) => target.uid);

    if (childTargetUids.length > 0) {
      await this.deleteTargetsByUids(childTargetUids);
    }

    await this.targetTypeRepository.deleteTargetTypesByTargetUids(uids);

    await this.repository.delete({ uid: In(uids) });
  }

  async updateParentTargetWeight(parentTarget: TargetEntity): Promise<void> {
    if (parentTarget) {
      parentTarget.weight = parentTarget.children.reduce((sum, child) => sum + (child.weight || 0), 0);

      await this.repository.save(parentTarget);
    }
  }
}
