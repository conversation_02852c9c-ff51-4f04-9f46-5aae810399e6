import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProposalCommentsEntity } from '../entities/proposal-comments.entity';
import { QueryFailedError } from 'typeorm';
import { BaseRepository } from './base.repository';
import { ProposalComments } from 'src/types';

@Injectable()
export class ProposalCommentsRepository extends BaseRepository<ProposalCommentsEntity> {
  constructor(
    @InjectRepository(ProposalCommentsEntity)
    public repository: Repository<ProposalCommentsEntity>
  ) {
    super(repository);
  }

  private createCommentQueryBuilder() {
    return this.repository
      .createQueryBuilder('comment')
      .select(['comment.uid', 'comment.message', 'comment.createdAt', 'employee.globalId', 'employee.name'])
      .leftJoin('comment.employee', 'employee');
  }

  private mapEntityToProposalComment(entity: ProposalCommentsEntity): ProposalComments {
    return {
      id: entity.uid,
      author: {
        globalId: entity.employee?.globalId?.toString() ?? '',
        name: entity.employee?.name ?? ''
      },
      message: entity.message,
      createdAt: entity.createdAt
    };
  }

  async findMessagesWithPagination(
    proposalId: string,
    pageNumber: number,
    pageSize: number
  ): Promise<{
    data: ProposalComments[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    const qb = this.createCommentQueryBuilder()
      .where('comment.proposalId = :proposalId', { proposalId })
      .orderBy('comment.createdAt', 'DESC')
      .skip((pageNumber - 1) * pageSize)
      .take(pageSize);

    const [entities, totalRecords] = await qb.getManyAndCount();

    const data: ProposalComments[] = entities.map((entity) => this.mapEntityToProposalComment(entity));

    return {
      data,
      pageNumber,
      pageSize,
      totalRecords
    };
  }

  async findCommentByUid(uid: string): Promise<ProposalComments | null> {
    const entity = await this.createCommentQueryBuilder().where('comment.uid = :uid', { uid }).getOne();

    return entity ? this.mapEntityToProposalComment(entity) : null;
  }

  async createMessage(proposalId: string, message: string, employeeId: string, targetId?: string): Promise<ProposalComments> {
    try {
      const saved = await this.repository.save({
        proposalId,
        message,
        employeeId,
        targetId: targetId ?? null
      });

      if (!saved || !saved.uid) {
        throw new Error('Failed to create comment');
      }

      const comment = await this.findCommentByUid(saved.uid);

      return comment;
    } catch (err: unknown) {
      const isQueryFailed =
        err instanceof QueryFailedError || (err && typeof err === 'object' && 'name' in err && err.name === 'QueryFailedError');
      if (isQueryFailed && err && typeof err === 'object' && 'message' in err && typeof err.message === 'string') {
        if (err.message.includes('FK_proposal_comments_proposalId') || err.message.includes('proposalId')) {
          throw new NotFoundException({ type: 'proposal_not_found', message: `Proposal with id ${proposalId} does not exist` });
        }
        if (err.message.includes('FK_proposal_comments_employeeId') || err.message.includes('employeeId')) {
          throw new NotFoundException({ type: 'employee_not_found', message: `Employee with id ${employeeId} does not exist` });
        }
        if (err.message.includes('FK_proposal_comments_targetId') || err.message.includes('targetId')) {
          throw new NotFoundException({ type: 'target_not_found', message: `Target with id ${targetId} does not exist` });
        }
      }

      throw err;
    }
  }

  async deleteMessage(id: string) {
    await this.repository.createQueryBuilder().softDelete().from(ProposalCommentsEntity).where('uid = :id', { id }).execute();
  }
}
