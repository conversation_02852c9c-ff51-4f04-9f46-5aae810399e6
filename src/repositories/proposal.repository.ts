import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProposalEntity } from '../entities/proposals.entity';
import { BaseRepository } from './base.repository';
import { TargetRepository } from './target.repository';
import { FindProposalsFilters } from 'src/types';
import { ProposalStatus } from '../enums';

@Injectable()
export class ProposalRepository extends BaseRepository<ProposalEntity> {
  constructor(
    @InjectRepository(ProposalEntity)
    public repository: Repository<ProposalEntity>,
    public targetRepository: TargetRepository
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<ProposalEntity | null> {
    return this.buildProposalQueryWithRelations(this.repository.createQueryBuilder('proposal'))
      .where('targets.parentTarget IS NULL')
      .andWhere('proposal.uid = :uid', { uid })
      .getOne();
  }

  async findAllProposals(
    { employeeIdentifier, status, zones, funcs, year, sltLevel, sltName }: FindProposalsFilters,
    pageNumber: number = 1,
    pageSize: number = 9
  ): Promise<{ data: ProposalEntity[]; pageNumber: number; pageSize: number; totalRecords: number }> {
    const baseQuery = this.repository
      .createQueryBuilder('proposal')
      .leftJoin('proposal.employee', 'employee')
      .leftJoin('employee.employee', 'emp')
      .leftJoin('emp.employeeProfile', 'employeeProfile')
      .leftJoin('employeeProfile.role', 'role')
      .leftJoin('role.zone', 'zone')
      .select(['proposal.uid'])
      .groupBy('proposal.uid');

    this.applyFilters({ employeeIdentifier, status, zones, funcs, year, sltLevel, sltName }, baseQuery);

    const totalRecords = await baseQuery.getCount();

    const paginatedProposals = await baseQuery
      .limit(pageSize)
      .offset((pageNumber - 1) * pageSize)
      .getMany();

    if (paginatedProposals.length === 0) {
      return { data: [], pageNumber, pageSize, totalRecords };
    }

    const proposalUids = paginatedProposals.map((p) => p.uid);

    const proposals = await this.buildProposalQueryWithRelations(this.repository.createQueryBuilder('proposal'))
      .where('proposal.uid IN (:...proposalUids)', { proposalUids })
      .andWhere('targets.parentTarget IS NULL')
      .getMany();

    return { data: proposals, pageNumber, pageSize, totalRecords };
  }

  private applyFilters(
    { employeeIdentifier, status, zones, funcs, year, sltLevel, sltName }: FindProposalsFilters,
    query: ReturnType<Repository<ProposalEntity>['createQueryBuilder']>
  ) {
    if (employeeIdentifier !== undefined && employeeIdentifier !== null && employeeIdentifier !== '') {
      if (typeof employeeIdentifier === 'number' || !isNaN(Number(employeeIdentifier))) {
        console.log('Employee Identifier is a number:', employeeIdentifier);
        query.andWhere('employee.globalId = :employeeIdentifier', { employeeIdentifier: employeeIdentifier });
      } else {
        query.andWhere('employee.name LIKE :employeeIdentifier', {
          employeeIdentifier: `%${employeeIdentifier}%`
        });
      }
    }

    const STATUS_GROUP_IN_PROGRESS = [
      ProposalStatus.IN_PROGRESS_PROPOSAL,
      ProposalStatus.IN_PROGRESS_FEEDBACK,
      ProposalStatus.IN_PROGRESS_FINAL
    ];

    if (status && status.length > 0) {
      const statuses = Array.isArray(status) ? status : [status];

      const proposalStatuses = statuses.flatMap((s) => {
        if (s === 'IN_PROGRESS') {
          return STATUS_GROUP_IN_PROGRESS;
        }
        return [s];
      });

      query.andWhere('proposal.status IN (:...proposalStatuses)', { proposalStatuses });
    }

    if (zones && zones.length > 0) {
      const employeeZones = Array.isArray(zones) ? zones : [zones];
      query.andWhere('zone.uid IN (:...employeeZones)', { employeeZones });
    }

    if (funcs && funcs.length > 0) {
      const employeeFunctions = Array.isArray(funcs) ? funcs : [funcs];
      query.andWhere('employee.businessFunction IN (:...employeeFunctions)', { employeeFunctions });
    }

    if (year) {
      query.andWhere('proposal.dateStart >= :startDate AND proposal.dateEnd <= :endDate', {
        startDate: new Date(year, 0, 1),
        endDate: new Date(year, 11, 31)
      });
    } else {
      const currentYear = new Date().getFullYear();
      query.andWhere('proposal.dateStart >= :startDate AND proposal.dateEnd <= :endDate', {
        startDate: new Date(currentYear, 0, 1),
        endDate: new Date(currentYear, 11, 31)
      });
    }

    if (sltLevel && sltLevel.length > 0) {
      const sltLevels = Array.isArray(sltLevel) ? sltLevel : [sltLevel];
      query.andWhere('employee.sltLevel IN (:...sltLevels)', { sltLevels });
    }

    if (sltName && sltName.length > 0) {
      const sltNames = Array.isArray(sltName) ? sltName : [sltName];
      query
        .innerJoin('employee.employee', 'em')
        .innerJoin('em.managerLevelSlt', 'manager')
        .andWhere('manager.uuid IN (:...sltNames)', { sltNames });
    }
  }

  async createProposal(proposal: ProposalEntity): Promise<ProposalEntity> {
    const savedProposal = await this.repository.save(proposal);
    return savedProposal;
  }

  async updateProposal(uid: string, proposal: Partial<ProposalEntity>): Promise<ProposalEntity> {
    const { targets, ...regularFields } = proposal;

    if (Object.keys(regularFields).length > 0) {
      await this.repository.update({ uid }, regularFields);
    }

    if (targets) {
      const existingProposal = await this.repository.findOne({
        where: { uid },
        relations: ['targets']
      });

      if (existingProposal) {
        existingProposal.targets = targets;
        await this.repository.save(existingProposal);
      }
    }

    return this.findByUid(uid) as Promise<ProposalEntity>;
  }

  async deleteProposal(uid: string): Promise<void> {
    await this.repository.delete({ uid });
  }

  async deleteTargetsFromProposalByUids(proposalUid: string, targetsUids: string[]): Promise<ProposalEntity> {
    await this.targetRepository.deleteTargetsByUids(targetsUids);

    return this.findByUid(proposalUid) as Promise<ProposalEntity>;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private buildProposalQueryWithRelations(queryBuilder: any): any {
    return queryBuilder
      .leftJoinAndSelect('proposal.employee', 'employee')
      .leftJoinAndSelect('employee.employee', 'em')
      .leftJoinAndSelect('em.managerLevelSlt', 'manager')
      .leftJoinAndSelect('proposal.targets', 'targets')
      .leftJoinAndSelect('targets.children', 'children')
      .leftJoinAndSelect('children.targetTypes', 'childTargetTypes')
      .leftJoinAndSelect('targets.deliverable', 'deliverable')
      .leftJoinAndSelect('deliverable.deliverableType', 'deliverableType')
      .leftJoinAndSelect('children.deliverable', 'childDeliverable')
      .leftJoinAndSelect('childDeliverable.deliverableType', 'childDeliverableType')
      .leftJoinAndSelect('targets.targetTypes', 'targetTypes')
      .select([
        'proposal',
        'employee',
        'manager',
        'targets.uid',
        'targets.weight',
        'targets.scope',
        'children.uid',
        'children.weight',
        'children.scope',
        'deliverable.uid',
        'deliverable.name',
        'deliverable.businessFunction',
        'deliverable.calculationMethod',
        'deliverable.definition',
        'childDeliverable.uid',
        'childDeliverable.name',
        'childDeliverable.businessFunction',
        'childDeliverable.calculationMethod',
        'childDeliverable.definition',
        'deliverableType.code',
        'childTargetTypes.uid',
        'childTargetTypes.type',
        'childTargetTypes.agree',
        'childDeliverableType.code',
        'targetTypes.uid',
        'targetTypes.type',
        'targetTypes.agree'
      ]);
  }

  async getFilters(): Promise<{
    data: {
      status: { label: string; value: string }[];
      zones: { label: string; value: string }[];
      functions: { label: string; value: string }[];
      sltLevels: { label: string; value: string }[];
      sltNames: { label: string; value: string }[];
    };
  }> {
    const [statuses, zones, functions, sltLevels, sltNames] = await Promise.all([
      this.repository.createQueryBuilder('proposal').select('DISTINCT proposal.status', 'status').getRawMany(),

      this.repository
        .createQueryBuilder('proposal')
        .innerJoin('proposal.employee', 'proposalEmployee')
        .innerJoin('proposalEmployee.employee', 'employee')
        .innerJoin('employee.employeeProfile', 'ep')
        .innerJoin('ep.role', 'role')
        .innerJoin('role.zone', 'zone')
        .select(['DISTINCT zone.uid AS value', 'zone.name AS label'])
        .getRawMany(),

      this.repository
        .createQueryBuilder('proposal')
        .innerJoin('proposal.employee', 'proposalEmployee')
        .select('DISTINCT proposalEmployee.businessFunction', 'function')
        .getRawMany(),

      this.repository
        .createQueryBuilder('proposal')
        .innerJoin('proposal.employee', 'proposalEmployee')
        .select('DISTINCT proposalEmployee.sltLevel', 'sltLevel')
        .getRawMany(),

      this.repository
        .createQueryBuilder('proposal')
        .innerJoin('proposal.employee', 'proposalEmployee')
        .innerJoin('proposalEmployee.employee', 'employee')
        .innerJoin('employee.managerLevelSlt', 'manager')
        .select(['DISTINCT manager.uuid AS value', "CONCAT(manager.str_firstname, ' ', manager.str_lastname) AS label"])
        .groupBy('manager.uuid, manager.str_firstname, manager.str_lastname')
        .getRawMany()
    ]);

    const STATUS_GROUP_IN_PROGRESS = [
      ProposalStatus.IN_PROGRESS_PROPOSAL,
      ProposalStatus.IN_PROGRESS_FEEDBACK,
      ProposalStatus.IN_PROGRESS_FINAL
    ];

    const STATUS_LABELS: Record<string, string> = {
      IN_PROGRESS: 'In Progress',
      NOT_STARTED: 'Not Started',
      COMPLETED: 'Completed'
    };

    return {
      data: {
        status: statuses
          .map((s) => {
            if (STATUS_GROUP_IN_PROGRESS.includes(s.status as ProposalStatus)) {
              return { label: STATUS_LABELS['IN_PROGRESS'], value: 'IN_PROGRESS' };
            }

            const label = STATUS_LABELS[s.status] ?? s.status;
            return { label, value: s.status };
          })
          .filter((item, index, self) => index === self.findIndex((t) => t.value === item.value)),
        zones,
        functions: functions.map((f) => ({ label: f.function, value: f.function })),
        sltLevels: sltLevels.map((s) => ({ label: s.sltLevel, value: s.sltLevel })),
        sltNames
      }
    };
  }
}
