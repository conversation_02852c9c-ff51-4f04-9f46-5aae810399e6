import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableTypeEntity } from '../entities/deliverables-types.entity';

@Injectable()
export class DeliverableTypeRepository {
  constructor(
    @InjectRepository(DeliverableTypeEntity)
    public repository: Repository<DeliverableTypeEntity>
  ) {}

  async findByCode(code: string): Promise<DeliverableTypeEntity | null> {
    return this.repository.findOne({ where: { code } });
  }

  async findAll(): Promise<DeliverableTypeEntity[]> {
    return this.repository.find();
  }
}
