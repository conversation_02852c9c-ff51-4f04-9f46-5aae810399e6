import { FindOptionsWhere, Repository, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { BaseEntity } from '../entities/base.entity';

export class BaseRepository<T extends BaseEntity> {
  constructor(protected readonly repository: Repository<T>) {}

  /**
   * Creates an entity
   * @param entity T
   * @returns T
   */
  create(entity: T): Promise<T> {
    return this.repository.save(entity);
  }

  /**
   * Updates an entity
   * @param primaryKey string|number
   * @param entity T
   * @returns T
   */
  update = async (primaryKeyColumn: string, primaryKeyValue: string | number, entity: T) => {
    const existingEntity = await this.repository.findOneBy({
      [primaryKeyColumn]: primaryKeyValue
    } as unknown as FindOptionsWhere<T>);
    const updatedEntity = { ...existingEntity, ...entity };
    return await this.repository.save(updatedEntity);
  };

  /**
   * Updates entities by filter criteria
   * @param options FindOptionsWhere<T> - The filter criteria
   * @param entity QueryDeepPartialEntity<T> - The partial entity data to update
   * @returns Promise<UpdateResult> - The update result
   */
  updateByFilter = (options: FindOptionsWhere<T>, entity: QueryDeepPartialEntity<T>): Promise<UpdateResult> =>
    this.repository.update(options, entity);

  /**
   * Soft deletes an entity by uid
   * @param uid string|number - The unique identifier of the entity to delete
   * @param deletedBy string - UUID of the user performing the deletion
   */
  async delete(uid: string | number, deletedBy: string): Promise<void> {
    await this.repository.update(
      { uid } as unknown as FindOptionsWhere<T>,
      {
        deletedBy
        // deletedAt is automatically handled by @DeleteDateColumn when we call softDelete
      } as unknown as QueryDeepPartialEntity<T>
    );

    // Use TypeORM's soft delete to properly set deletedAt
    await this.repository.softDelete({ uid } as unknown as FindOptionsWhere<T>);
  }

  /**
   * Finds all entities from the repository
   * @returns Promise<T[]> A list of all entities
   */
  async find(): Promise<T[]> {
    return this.repository.find();
  }

  /**
   * Finds all active entities (not soft deleted)
   * @returns Promise<T[]> A list of all active entities
   */
  async findActive(): Promise<T[]> {
    return this.repository.find({
      where: { deletedAt: null } as unknown as FindOptionsWhere<T>
    });
  }
}
