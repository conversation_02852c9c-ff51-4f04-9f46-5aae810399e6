import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmployeeEntity } from '../entities/employees.entity';

@Injectable()
export class EmployeeRepository {
  constructor(
    @InjectRepository(EmployeeEntity)
    public repository: Repository<EmployeeEntity>
  ) {}

  public async findById(uuid: string): Promise<EmployeeEntity | null> {
    return this.repository.findOne({
      where: { uuid }
    });
  }

  public async findByName(search: string): Promise<EmployeeEntity[]> {
    return this.repository
      .createQueryBuilder('employee')
      .where(`LOWER(CONCAT(employee.str_firstname, ' ', employee.str_lastname)) LIKE LOWER(:fullName)`, {
        fullName: `%${search}%`
      })
      .getMany();
  }

  public async findByGlobalId(globalId: number): Promise<EmployeeEntity | null> {
    return this.repository.findOne({
      where: { globalId }
    });
  }
}
