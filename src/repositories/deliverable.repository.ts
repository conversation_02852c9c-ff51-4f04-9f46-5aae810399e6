import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableEntity } from '../entities/deliverables.entity';
import { BaseRepository } from './base.repository';
import { SortBy, OrderBy } from '../enums';
import { SearchParams } from '../interfaces';
import { DeliverableWithType } from '../types';

@Injectable()
export class DeliverableRepository extends BaseRepository<DeliverableEntity> {
  constructor(
    @InjectRepository(DeliverableEntity)
    public repository: Repository<DeliverableEntity>
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<DeliverableWithType | null> {
    const deliverable = await this.repository.findOne({
      where: { uid },
      relations: ['deliverableType', 'deliverables']
    });

    if (!deliverable) {
      return null;
    }

    const { deliverableType, ...deliverableData } = deliverable;
    return {
      ...deliverableData,
      type: deliverableType?.code
    };
  }

  async findCompactStandaloneWithFilters(
    search?: string,
    functions?: string[],
    isActive?: boolean,
    types?: string[],
    sortBy?: SortBy,
    orderBy?: OrderBy,
    pageNumber: number = 1,
    pageSize: number = 10
  ): Promise<object> {
    const query = this.repository
      .createQueryBuilder('deliverable')
      .leftJoinAndSelect('deliverable.owners', 'deliverables_owners')
      .leftJoinAndSelect('deliverable.deliverableType', 'deliverables_types')
      .leftJoinAndSelect('deliverable.deliverables', 'deliverables_deliverables')
      .where(`deliverable.deliverableType.code NOT IN ('SCOPED_PROJECT_YES_NO')`);

    if (isActive !== undefined) {
      query.andWhere('deliverable.isActive = :isActive', {
        isActive: isActive ? 1 : 0
      });
    } else {
      query.andWhere('deliverable.isActive = 1');
    }

    if (search) {
      const kpiTypes = ['KPI'];
      const projectTypes = ['PROJECT'];

      let kpiTypesFilter: string[] = [];
      let projectTypesFilter: string[] = [];

      if (types && types.length > 0) {
        kpiTypesFilter = types.filter((type) => kpiTypes.includes(type));
        projectTypesFilter = types.filter((type) => projectTypes.includes(type));
      } else {
        kpiTypesFilter = kpiTypes;
        projectTypesFilter = projectTypes;
      }

      const searchConditions: string[] = [];
      const searchParams: SearchParams = { search: `%${search}%` };

      if (kpiTypesFilter.length > 0) {
        searchConditions.push(
          `(deliverable.deliverableType.code IN (:...kpiTypesFilter) AND (deliverable.name LIKE :search OR deliverable.definition LIKE :search OR deliverable.calculationMethod LIKE :search))`
        );
        searchParams.kpiTypesFilter = kpiTypesFilter;
      }

      if (projectTypesFilter.length > 0) {
        searchConditions.push(
          `(deliverable.deliverableType.code IN (:...projectTypesFilter) AND (deliverable.name LIKE :search OR deliverable.definition LIKE :search OR deliverables_deliverables.name LIKE :search))`
        );
        searchParams.projectTypesFilter = projectTypesFilter;
      }

      if (searchConditions.length > 0) {
        query.andWhere(`(${searchConditions.join(' OR ')})`, searchParams);
      }
    }

    if (functions && functions.length > 0) {
      query.andWhere('deliverable.businessFunction IN (:...functions)', {
        functions
      });
    }

    if (types && types.length > 0) {
      query.andWhere('deliverable.deliverableType.code IN (:...types)', {
        types
      });
    }

    // TODO: replace frequency with suage when this field is added
    const sortField = sortBy === SortBy.USAGE ? 'deliverable.frequency' : 'deliverable.name';
    const sortDirection = orderBy === OrderBy.DESC ? 'DESC' : 'ASC';

    query.orderBy(sortField, sortDirection);

    const offset = (pageNumber - 1) * pageSize;

    const [entities, totalRecords] = await Promise.all([query.skip(offset).take(pageSize).getMany(), query.getCount()]);

    const data = entities.map((entity) => ({
      uid: entity.uid,
      name: entity.name,
      businessFunction: entity.businessFunction,
      isActive: entity.isActive,
      calculationMethod: entity.calculationMethod,
      paValue: entity.paValue,
      definition: entity.definition,
      frequency: entity.frequency,
      type: entity.deliverableType?.code,
      deliverableName: entity.deliverables?.[0]?.name,
      deliverables:
        entity.deliverables?.map((d) => ({
          uid: d.uid,
          name: d.name
        })) || [],
      owners: entity.owners || []
    }));

    return {
      data,
      pageNumber,
      pageSize,
      totalRecords
    };
  }

  async findByUids(uids: string[]): Promise<DeliverableEntity[]> {
    if (!uids || uids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('deliverable')
      .select(['deliverable.uid', 'deliverable.name'])
      .where('deliverable.uid IN (:...uids)', { uids })
      .getMany();
  }

  async getFunctions(): Promise<{ function: string }[]> {
    const result = await this.repository
      .createQueryBuilder('deliverable')
      .select('deliverable.businessFunction', 'function')
      .where('deliverable.businessFunction IS NOT NULL')
      .andWhere('deliverable.businessFunction != :emptyString', { emptyString: '' })
      .distinct(true)
      .getRawMany();

    console.log('Functions retrieved:', result);
    return result;
  }
}
