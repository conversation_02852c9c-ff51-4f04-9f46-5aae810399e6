import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableOwnerEntity } from '../entities/deliverables-owners.entity';
import { BaseRepository } from './base.repository';

@Injectable()
export class DeliverableOwnerRepository extends BaseRepository<DeliverableOwnerEntity> {
  constructor(
    @InjectRepository(DeliverableOwnerEntity)
    public repository: Repository<DeliverableOwnerEntity>
  ) {
    super(repository);
  }

  async createMany(entities: DeliverableOwnerEntity[]): Promise<DeliverableOwnerEntity[]> {
    return this.repository.save(entities);
  }
}
