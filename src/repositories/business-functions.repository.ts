import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessFunctionEntity } from '../entities/business-functions.entity';

@Injectable()
export class BusinessFunctionRepository {
  constructor(
    @InjectRepository(BusinessFunctionEntity)
    public repository: Repository<BusinessFunctionEntity>
  ) {}

  async findByCode(code: string): Promise<BusinessFunctionEntity | null> {
    return this.repository.findOne({ where: { code } });
  }

  async findAll(): Promise<BusinessFunctionEntity[]> {
    return this.repository.find();
  }
}
