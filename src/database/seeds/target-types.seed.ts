import { IsNull, type DataSource } from 'typeorm';
import { TargetEntity, TargetTypeEntity } from '../../entities';
import { TargetType } from '../../enums';

export const runTargetTypesSeed = async (dataSource: DataSource) => {
  const targetRepo = dataSource.getRepository(TargetEntity);
  const targetTypeRepo = dataSource.getRepository(TargetTypeEntity);

  console.log('🚀 Seeding Target Types...');

  const parentTargets = await targetRepo.find({
    where: {
      uidParentTarget: IsNull()
    }
  });

  console.log(`Found ${parentTargets.length} parent targets`);

  const targetTypes: Partial<TargetTypeEntity>[] = [];

  for (const parentTarget of parentTargets) {
    targetTypes.push({
      uidTarget: parentTarget.uid,
      type: TargetType.PROPOSAL
    });
  }

  await targetTypeRepo.save(targetTypes);

  console.log(`✅ ${targetTypes.length} Target Types seeded for parent targets!`);
};
