import { dataSource } from '../../configs/typeorm.datasource';
import { runDeliverablesTypesSeed } from './deliverables-types.seed';
import { runBusinessFunctionsSeed } from './business-functions.seed';
import { runProposalEmployeesSeed } from './proposal-employees.seed';
import { runProposalsSeed } from './proposals.seed';
import { runTargetsSeed } from './targets.seed';
import { runTargetTypesSeed } from './target-types.seed';

type SeedFn = (ds: typeof dataSource) => Promise<void>;

const registry: Record<string, SeedFn> = {
  'deliverable-types': runDeliverablesTypesSeed,
  'business-functions': runBusinessFunctionsSeed,
  'proposal-employees': runProposalEmployeesSeed,
  proposals: runProposalsSeed,
  targets: runTargetsSeed,
  'target-types': runTargetTypesSeed
};

async function runSeed() {
  const only = process.argv.find((a) => a.startsWith('--only='))?.split('=')[1];

  await dataSource.initialize();
  console.log('📦 Database connected.');

  if (only) {
    const seed = registry[only];
    if (!seed) {
      throw new Error(`Seed not found: ${only}`);
    }
    await seed(dataSource);
  } else {
    await runDeliverablesTypesSeed(dataSource);
    await runBusinessFunctionsSeed(dataSource);
    await runProposalEmployeesSeed(dataSource);
    await runProposalsSeed(dataSource);
    await runTargetsSeed(dataSource);
    await runTargetTypesSeed(dataSource);
  }

  await dataSource.destroy();
  console.log('✅ Seed completed!');
}

runSeed().catch((err) => {
  console.error('❌ Seed failed', err);
  process.exit(1);
});
