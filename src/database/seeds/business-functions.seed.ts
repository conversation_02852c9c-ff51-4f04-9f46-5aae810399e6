import type { DataSource } from 'typeorm';
import { BusinessFunctionEntity } from '../../entities';

export const runBusinessFunctionsSeed = async (dataSource: DataSource) => {
  const repo = dataSource.getRepository(BusinessFunctionEntity);

  console.log('🚀 Seeding Business Functions...');

  const BUSINESS_FUNCTIONS = [
    'Finance',
    'Operations',
    'Sales',
    'Marketing',
    'Human Resources',
    'IT',
    'Customer Service',
    'Research and Development',
    'Legal',
    'Procurement',
  ] as const;

  const rows = BUSINESS_FUNCTIONS.map((label) => ({
    code: label
      .toUpperCase()
      .replace(/\s+/g, '_')
      .replace(/[^A-Z0-9_]/g, ''),
    label,
  }));

  await repo.upsert(rows, ['code']);

  console.log('✅ Seeded Business Functions!');
};