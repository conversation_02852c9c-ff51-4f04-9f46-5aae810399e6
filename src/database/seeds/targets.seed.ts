import type { DataSource } from 'typeorm';
import { TargetEntity, ProposalEntity } from '../../entities';
import { v4 as uuidv4 } from 'uuid';

export const runTargetsSeed = async (dataSource: DataSource) => {
  const targetRepo = dataSource.getRepository(TargetEntity);
  const proposalRepo = dataSource.getRepository(ProposalEntity);

  console.log('🚀 Seeding Targets...');

  const proposals = await proposalRepo.find();

  const deliverableIds = [
    '8F746EA9-B51D-4C72-8694-3775C21EB688',
    'A0D5725A-56A7-4CC5-AAA1-46ED71A3C0FF',
    '4F4E0206-00AE-4B70-A225-52945827F4B2',
    'A23E52FE-3EC4-4EB7-A015-5B4AC6E327EC',
    '683F65C1-7D9A-4244-8225-5E933EDB2F8E',
    '8325F823-E440-43CB-8C8D-7126F28BC86C',
    '57BE5B32-67C5-41D4-8F1B-7202EFBA1AEC',
    '8C8EDB66-488C-4244-B48F-7D851FA81FEA',
    '0D0B23BE-D772-4A7F-89B8-93B3E6624C94',
    '624DAFB6-AA65-4388-928B-93CB5E926D99',
    '08FE3C24-344F-4162-90BA-96D2FE4F6B14',
    'F55A6D18-1C75-4042-899B-A4310061D1E1',
    '501E71A1-2E38-41B1-BA2A-A9EA52A1BF63',
    '1640E1D4-6631-481B-9A34-C0FC8E470262',
    'B12A9F13-89D0-4405-8F99-D508E64CE62C',
    '236AD5CC-CDBE-4F25-B1DD-E4044C4C263D',
    '569856F8-935D-4B38-AD52-F0C1C1C5E83A',
    '93DB95E7-0E16-4B36-8391-F5591242570C',
    'CD33AF31-D8F6-4D25-B773-F5ED35BA5FED'
  ];

  const getRandomDeliverable = () => {
    const randomIndex = Math.floor(Math.random() * deliverableIds.length);
    return deliverableIds[randomIndex];
  };

  let totalTargets = 0;

  for (const proposal of proposals) {
    const targets: Partial<TargetEntity>[] = [];

    for (let parentIndex = 1; parentIndex <= 3; parentIndex++) {
      const parentTargetId = uuidv4();
      const parentWeight = Math.round((Math.random() * 40 + 10) * 100) / 100; // Random weight between 10-50

      targets.push({
        uid: parentTargetId,
        weight: parentWeight,
        scope: `Parent Target ${parentIndex} - ${proposal.uidEmployee?.substring(0, 8)}`,
        uidProposal: proposal.uid,
        uidDeliverable: null,
        uidParentTarget: null
      });

      for (let childIndex = 1; childIndex <= 2; childIndex++) {
        const childTargetId = uuidv4();
        const childWeight = Math.round((Math.random() * 20 + 5) * 100) / 100;

        targets.push({
          uid: childTargetId,
          weight: childWeight,
          scope: `Child Target ${parentIndex}.${childIndex} - ${proposal.uidEmployee?.substring(0, 8)}`,
          uidProposal: proposal.uid,
          uidDeliverable: getRandomDeliverable(),
          uidParentTarget: parentTargetId
        });
      }
    }

    await targetRepo.save(targets);
    totalTargets += targets.length;

    console.log(
      `✅ Proposal ${proposal.uidEmployee?.substring(0, 8)}: ${targets.length} targets saved (Total: ${totalTargets} targets)`
    );
  }

  console.log(`✅ ${totalTargets} Targets saved!`);

  console.log('✅ All Targets seeded successfully! (Target types will be created by separate migration)');
};
