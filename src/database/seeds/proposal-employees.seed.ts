import type { DataSource } from 'typeorm';
import { ProposalEmployeeEntity } from '../../entities/proposals-employees.entity';

export const runProposalEmployeesSeed = async (dataSource: DataSource) => {
  const proposalEmployeeRepo = dataSource.getRepository(ProposalEmployeeEntity);

  console.log('🚀 Seeding Proposal Employees...');

  // Real employee data from migration
  const employeeData = [
    {
      employeeUuid: '28B11DF3-ED14-4AFE-BC25-00E482511486',
      globalId: 676767,
      name: 'Delfina Biasutto',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '1'
    },
    {
      employeeUuid: '366F3422-C089-496A-A665-10B724D13ADF',
      globalId: 696969,
      name: '<PERSON>',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '1'
    },
    {
      employeeUuid: '7B49977F-46BE-4EC9-B17B-1ED819F07F78',
      globalId: 999999,
      name: '<PERSON><PERSON><PERSON>',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: '0A9CCCE4-A54E-4106-AE82-29D7B84E1707',
      globalId: 999999,
      name: 'Thiago Mattara',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: 'B221D0B2-44D1-4A89-B327-2C355B015520',
      globalId: 123124,
      name: 'Maycon Oliveira',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: 'B2B545D0-1389-406C-BE07-367DDEBE6EC7',
      globalId: 222222,
      name: 'Lucas Haddad',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: '2A987EAF-1809-4773-85FE-387E4977C462',
      globalId: 99836127,
      name: 'GUSTAVO FERNANDES SILVEIRA',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '4'
    },
    {
      employeeUuid: 'E8F6AEEB-BF7D-44C3-9ECE-4696EEA39A7E',
      globalId: 686868,
      name: 'Joao Rosa',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: '3C535857-7C7F-43A4-8D1E-68E0403C32AA',
      globalId: 123124,
      name: 'Maycon Oliveira',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '2'
    },
    {
      employeeUuid: '1D118797-E71E-40C8-8C3D-6A09BB91BAAD',
      globalId: 123124,
      name: 'Maycon Silva Oliveira',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '3'
    },
    {
      employeeUuid: '3C1CE7D2-4E18-49B6-9E8E-77D8CFD7E06C',
      globalId: 99824775,
      name: 'MATHEUS AFONSO JESUS LOPEZ',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '4'
    },
    {
      employeeUuid: 'B1DC6910-E500-4221-8C8D-9F1980D4E5C7',
      globalId: 99824775,
      name: 'MATHEUS AFONSO JESUS LOPEZ',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '4'
    },
    {
      employeeUuid: '68478BA3-889B-404A-A163-A0726390565B',
      globalId: 707070,
      name: 'Calos Estrela',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '1'
    },
    {
      employeeUuid: '1ADA5102-2C64-40D5-8B79-AA5107A83736',
      globalId: 99836127,
      name: 'GUSTAVO FERNANDES SILVEIRA',
      zone: 'GLOBAL',
      businessFunction: 'TECHNOLOGY',
      sltLevel: '4'
    }
  ];

  const proposalEmployees: Partial<ProposalEmployeeEntity>[] = employeeData.map((emp) => ({
    employeeUuid: emp.employeeUuid,
    globalId: emp.globalId,
    name: emp.name,
    sltLevel: emp.sltLevel,
    zone: emp.zone,
    businessFunction: emp.businessFunction
  }));

  await proposalEmployeeRepo.save(proposalEmployees);

  console.log(`✅ ${proposalEmployees.length} Proposal Employees seeded!`);
};
