import { DeliverableTypeEntity } from '../../entities';
import { DELIVERABLES_TYPES } from '../mocks/deliverables-types.mock';

export const runDeliverablesTypesSeed = async (dataSource) => {
  const deliverableTypeRepo = dataSource.getRepository(DeliverableTypeEntity);

  console.log('🚀 Seeding Deliverable Types...');

  for (const deliverableType of DELIVERABLES_TYPES) {
    await deliverableTypeRepo.save({ code: deliverableType });
  }

  console.log('✅ Deliverable Types Seeded!');
};
