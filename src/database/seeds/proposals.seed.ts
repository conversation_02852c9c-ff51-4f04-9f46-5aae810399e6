import type { DataSource } from 'typeorm';
import { ProposalEntity } from '../../entities';
import { ProposalStatus } from '../../enums';
import { v4 as uuidv4 } from 'uuid';

export const runProposalsSeed = async (dataSource: DataSource) => {
  const proposalRepo = dataSource.getRepository(ProposalEntity);

  console.log('🚀 Seeding Proposals...');

  const employeeIds = [
    'B221D0B2-44D1-4A89-B327-2C355B015520',
    '3C535857-7C7F-43A4-8D1E-68E0403C32AA',
    '1D118797-E71E-40C8-8C3D-6A09BB91BAAD',
    'B2B545D0-1389-406C-BE07-367DDEBE6EC7',
    '28B11DF3-ED14-4AFE-BC25-00E482511486',
    'E8F6AEEB-BF7D-44C3-9ECE-4696EEA39A7E',
    '366F3422-C089-496A-A665-10B724D13ADF',
    '68478BA3-889B-404A-A163-A0726390565B',
    '7B49977F-46BE-4EC9-B17B-1ED819F07F78',
    '0A9CCCE4-A54E-4106-AE82-29D7B84E1707',
    '3C1CE7D2-4E18-49B6-9E8E-77D8CFD7E06C',
    'B1DC6910-E500-4221-8C8D-9F1980D4E5C7',
    '2A987EAF-1809-4773-85FE-387E4977C462',
    '1ADA5102-2C64-40D5-8B79-AA5107A83736'
  ];

  const proposals: Partial<ProposalEntity>[] = [];

  for (const employeeId of employeeIds) {
    const startDate = new Date(2025, 0, 1);
    const endDate = new Date(2025, 11, 31);

    proposals.push({
      uid: uuidv4(),
      status: ProposalStatus.NOT_STARTED,
      uidEmployee: employeeId,
      dateStart: startDate,
      dateEnd: endDate
    });
  }

  await proposalRepo.save(proposals);

  console.log(`✅ ${proposals.length} Proposals seeded!`);
};
