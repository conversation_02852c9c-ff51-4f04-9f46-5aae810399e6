import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAgreeColumn1757447193915 implements MigrationInterface {
  name = 'AddAgreeColumn1757447193915';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tsc-dev"."target_types" ADD "agree" bit`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tsc-dev"."target_types" DROP COLUMN "agree"`);
  }
}
