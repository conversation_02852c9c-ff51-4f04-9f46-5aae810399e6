import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBusinessFunctions1755559983383 implements MigrationInterface {
    name = 'AddBusinessFunctions1755559983383'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables_business_functions" ("code" nvarchar(255) NOT NULL, "label" nvarchar(255) NOT NULL, CONSTRAINT "PK_0ee57bdd77a87dd9844d09588dc" PRIMARY KEY ("code"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables_business_functions"`);
    }

}
