import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialStructure1755521492620 implements MigrationInterface {
    name = 'InitialStructure1755521492620'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables_types" ("code" nvarchar(255) NOT NULL, CONSTRAINT "PK_83f43221c5232e0c29e648eda03" PRIMARY KEY ("code"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_63ff64b27aa42959f3daa527a3e" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_8de66953734c2f53cba2eae4f8a" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_43b1021ac6db2004d6992ab9ffb" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "buLevelAggregation" nvarchar(MAX), "businessFunction" nvarchar(255), "calculationMethod" nvarchar(MAX), "content" nvarchar(MAX), "dataSource" nvarchar(MAX), "dateEnd" datetime2, "dateStart" datetime2, "definition" nvarchar(MAX), "frequency" nvarchar(255), "isActive" bit NOT NULL CONSTRAINT "DF_a44a2f058328ec2c4658c67abe2" DEFAULT 1, "name" nvarchar(255) NOT NULL, "paValue" nvarchar(MAX), "deliverableType" nvarchar(255), CONSTRAINT "PK_63ff64b27aa42959f3daa527a3e" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables_owners" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_02e27ae35cc98f01dfdbaac8086" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_5e792053c3ac56ece10d86aeb9c" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_d05c8a6bc5dcb51a30217d319d0" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "employeeUuid" nvarchar(255), "email" nvarchar(255) NOT NULL, "globalId" int NOT NULL, "name" nvarchar(255) NOT NULL, "positionTitle" nvarchar(255) NOT NULL, CONSTRAINT "PK_02e27ae35cc98f01dfdbaac8086" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables_deliverables" ("deliverablesUid_1" uniqueidentifier NOT NULL, "deliverablesUid_2" uniqueidentifier NOT NULL, CONSTRAINT "PK_4f30ddfc8ec013ee8b22940c8a1" PRIMARY KEY ("deliverablesUid_1", "deliverablesUid_2"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0a4cb1c9e5e399683edf89c581" ON "tsc-dev"."deliverables_deliverables" ("deliverablesUid_1") `);
        await queryRunner.query(`CREATE INDEX "IDX_7a0357bb723db7bc563a45b4a7" ON "tsc-dev"."deliverables_deliverables" ("deliverablesUid_2") `);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."deliverables_deliverables_owners" ("deliverablesUid" uniqueidentifier NOT NULL, "deliverablesOwnersUid" uniqueidentifier NOT NULL, CONSTRAINT "PK_cf84a5f725b6f06d35718d243c3" PRIMARY KEY ("deliverablesUid", "deliverablesOwnersUid"))`);
        await queryRunner.query(`CREATE INDEX "IDX_001f26a06eb42a46bc2779afbb" ON "tsc-dev"."deliverables_deliverables_owners" ("deliverablesUid") `);
        await queryRunner.query(`CREATE INDEX "IDX_24c716f40b26fae38572d51184" ON "tsc-dev"."deliverables_deliverables_owners" ("deliverablesOwnersUid") `);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables" ADD CONSTRAINT "FK_00621a1187f01d36db1eea51e87" FOREIGN KEY ("deliverableType") REFERENCES "tsc-dev"."deliverables_types"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables" ADD CONSTRAINT "FK_0a4cb1c9e5e399683edf89c5810" FOREIGN KEY ("deliverablesUid_1") REFERENCES "tsc-dev"."deliverables"("uid") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables" ADD CONSTRAINT "FK_7a0357bb723db7bc563a45b4a7f" FOREIGN KEY ("deliverablesUid_2") REFERENCES "tsc-dev"."deliverables"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables_owners" ADD CONSTRAINT "FK_001f26a06eb42a46bc2779afbb7" FOREIGN KEY ("deliverablesUid") REFERENCES "tsc-dev"."deliverables"("uid") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables_owners" ADD CONSTRAINT "FK_24c716f40b26fae38572d51184b" FOREIGN KEY ("deliverablesOwnersUid") REFERENCES "tsc-dev"."deliverables_owners"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables_owners" DROP CONSTRAINT "FK_24c716f40b26fae38572d51184b"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables_owners" DROP CONSTRAINT "FK_001f26a06eb42a46bc2779afbb7"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables" DROP CONSTRAINT "FK_7a0357bb723db7bc563a45b4a7f"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables_deliverables" DROP CONSTRAINT "FK_0a4cb1c9e5e399683edf89c5810"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."deliverables" DROP CONSTRAINT "FK_00621a1187f01d36db1eea51e87"`);
        await queryRunner.query(`DROP INDEX "IDX_24c716f40b26fae38572d51184" ON "tsc-dev"."deliverables_deliverables_owners"`);
        await queryRunner.query(`DROP INDEX "IDX_001f26a06eb42a46bc2779afbb" ON "tsc-dev"."deliverables_deliverables_owners"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables_deliverables_owners"`);
        await queryRunner.query(`DROP INDEX "IDX_7a0357bb723db7bc563a45b4a7" ON "tsc-dev"."deliverables_deliverables"`);
        await queryRunner.query(`DROP INDEX "IDX_0a4cb1c9e5e399683edf89c581" ON "tsc-dev"."deliverables_deliverables"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables_deliverables"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables_owners"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."deliverables_types"`);
    }

}
