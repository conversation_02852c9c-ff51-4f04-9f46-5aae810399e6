import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProposalCommentsEmployeeReference1756394000000 implements MigrationInterface {
  name = 'UpdateProposalCommentsEmployeeReference1756394000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tsc-dev"."proposal_comments" DROP CONSTRAINT "FK_proposal_comments_employeeId"`);

    await queryRunner.query(
      `ALTER TABLE "tsc-dev"."proposal_comments" ADD CONSTRAINT "FK_proposal_comments_employeeId" FOREIGN KEY ("employeeId") REFERENCES "tsc-dev"."proposals_employees"("employeeUuid") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tsc-dev"."proposal_comments" DROP CONSTRAINT "FK_proposal_comments_employeeId"`);

    await queryRunner.query(
      `ALTER TABLE "tsc-dev"."proposal_comments" ADD CONSTRAINT "FK_proposal_comments_employeeId" FOREIGN KEY ("employeeId") REFERENCES "tsc-dev"."employees"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }
}
