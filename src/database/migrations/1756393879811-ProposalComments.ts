import { MigrationInterface, QueryRunner } from "typeorm";

export class ProposalComments1756393879811 implements MigrationInterface {
    name = 'ProposalComments1756393879811'


    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tsc-dev"."proposal_comments" (
            "uid" uniqueidentifier NOT NULL CONSTRAINT "DF_proposal_comment" DEFAULT NEWID(),
            "deletedAt" datetime2,
            "createdAt" datetime2 NOT NULL CONSTRAINT "DF_createdAt_proposal_comments" DEFAULT getdate(),
            "updatedAt" datetime2 CONSTRAINT "DF_updatedAt_proposal_comments" DEFAULT getdate(),
            "deletedBy" uniqueidentifier,
            "createdBy" uniqueidentifier,
            "updatedBy" uniqueidentifier,
            "proposalId" uniqueidentifier NOT NULL,
            "targetId" uniqueidentifier NULL,
            "message" nvarchar(max) NOT NULL,
            "employeeId" uniqueidentifier NOT NULL,
            CONSTRAINT "FK_proposal_comments_proposalId" FOREIGN KEY ("proposalId") REFERENCES "tsc-dev"."proposal"("uid"),
            CONSTRAINT "FK_proposal_comments_targetId" FOREIGN KEY ("targetId") REFERENCES "tsc-dev"."targets"("uid"),
            CONSTRAINT "FK_proposal_comments_employeeId" FOREIGN KEY ("employeeId") REFERENCES "tsc-dev"."employees"("uuid"),
            CONSTRAINT "PK_proposal_comments_uid" PRIMARY KEY ("uid"))
        )`);
        await queryRunner.query(`CREATE INDEX "IDX_proposal_comments_proposalId" ON "tsc-dev"."proposal_comments" ("proposalId")`);
        await queryRunner.query(`CREATE INDEX "IDX_proposal_comments_targetId" ON "tsc-dev"."proposal_comments" ("targetId")`);
        await queryRunner.query(`CREATE INDEX "IDX_proposal_comments_createdAt" ON "tsc-dev"."proposal_comments" ("createdAt")`);
        await queryRunner.query(`CREATE INDEX "IDX_proposal_comments_employeeId" ON "tsc-dev"."proposal_comments" ("employeeId")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "IDX_proposal_comments_proposalId" ON "tsc-dev"."proposal_comments"`);
        await queryRunner.query(`DROP INDEX "IDX_proposal_comments_targetId" ON "tsc-dev"."proposal_comments"`);
        await queryRunner.query(`DROP INDEX "IDX_proposal_comments_createdAt" ON "tsc-dev"."proposal_comments"`);
        await queryRunner.query(`DROP INDEX "IDX_proposal_comments_employeeId" ON "tsc-dev"."proposal_comments"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."proposal_comments"`);
    }

}
