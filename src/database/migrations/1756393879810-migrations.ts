import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1756393879810 implements MigrationInterface {
    name = 'Migrations1756393879810'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tsc-dev"."proposals_employees" ("employeeUuid" uniqueidentifier NOT NULL, "globalId" int NOT NULL, "name" nvarchar(255) NOT NULL, "sltLevel" nvarchar(255) NOT NULL, "zone" nvarchar(255) NOT NULL, "businessFunction" nvarchar(255) NOT NULL, CONSTRAINT "PK_e19301640373bd237b6ea3e644f" PRIMARY KEY ("employeeUuid"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."proposal" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_6ea08c9fd0972d326757e7beeb0" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_8a8054b34ba533849dab5519e96" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_b4e141cfdaa6ca4d0efc12586a2" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "status" nvarchar(50) CONSTRAINT CHK_5eb2a2313a2ac4ca5fee564838_ENUM CHECK(status IN ('NOT_STARTED','IN_PROGRESS_PROPOSAL','IN_PROGRESS_FEEDBACK','IN_PROGRESS_FINAL','COMPLETED')) NOT NULL CONSTRAINT "DF_02db1db1164f68f57ea6ee8d2a4" DEFAULT 'NOT_STARTED', "uidEmployee" uniqueidentifier, "dateStart" date, "dateEnd" date, CONSTRAINT "PK_6ea08c9fd0972d326757e7beeb0" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."target_types" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_72ed2841c6d637464506e999513" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_5b00755092b3c948575b310fd2d" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_aafecef89b5a72462fcece9dc53" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "uidTarget" uniqueidentifier NOT NULL, "type" nvarchar(255) NOT NULL, CONSTRAINT "PK_72ed2841c6d637464506e999513" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."targets" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_14456824966baed6e9e4cc2b8f8" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_aea832ec28cafc15d8f4c49d855" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_6200db833895636f5fe336f247b" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "weight" decimal, "scope" nvarchar(255), "uidDeliverable" uniqueidentifier, "uidParentTarget" uniqueidentifier, "uidProposal" uniqueidentifier, CONSTRAINT "PK_14456824966baed6e9e4cc2b8f8" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."proposals_employees" ADD CONSTRAINT "FK_e19301640373bd237b6ea3e644f" FOREIGN KEY ("employeeUuid") REFERENCES "tsc-dev"."employees"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."proposal" ADD CONSTRAINT "FK_4185bafc18f5895541378c85f2b" FOREIGN KEY ("uidEmployee") REFERENCES "tsc-dev"."proposals_employees"("employeeUuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_types" ADD CONSTRAINT "FK_b904537b463704b9326be48f904" FOREIGN KEY ("uidTarget") REFERENCES "tsc-dev"."targets"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" ADD CONSTRAINT "FK_77ed01724ab5ce735aa19a06131" FOREIGN KEY ("uidProposal") REFERENCES "tsc-dev"."proposal"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" ADD CONSTRAINT "FK_cdd06ea15a9c9942b847160b992" FOREIGN KEY ("uidDeliverable") REFERENCES "tsc-dev"."deliverables"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" ADD CONSTRAINT "FK_a81d575746be6fbc62b5532d2e8" FOREIGN KEY ("uidParentTarget") REFERENCES "tsc-dev"."targets"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" DROP CONSTRAINT "FK_a81d575746be6fbc62b5532d2e8"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" DROP CONSTRAINT "FK_cdd06ea15a9c9942b847160b992"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."targets" DROP CONSTRAINT "FK_77ed01724ab5ce735aa19a06131"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_types" DROP CONSTRAINT "FK_b904537b463704b9326be48f904"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."proposal" DROP CONSTRAINT "FK_4185bafc18f5895541378c85f2b"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."proposals_employees" DROP CONSTRAINT "FK_e19301640373bd237b6ea3e644f"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."targets"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."target_types"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."proposal"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."proposals_employees"`);
    }

}
