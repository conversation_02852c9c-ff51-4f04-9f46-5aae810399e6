import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDraftTypeToDeliverablesTypes1757096591791 implements MigrationInterface {
  name = 'AddDraftTypeToDeliverablesTypes1757096591791';

  public async up(queryRunner: QueryRunner): Promise<void> {
    queryRunner.query(`
      IF NOT EXISTS (SELECT 1 FROM [tsc-dev].deliverables_types WHERE code = 'DRAFT')
      BEGIN
          INSERT INTO [tsc-dev].deliverables_types (code) VALUES ('DRAFT');
      END
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    queryRunner.query(`
      DELETE FROM [tsc-dev].deliverables_types WHERE code = 'DRAFT';
    `);
  }
}
