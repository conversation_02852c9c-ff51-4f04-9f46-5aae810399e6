trigger:
  branches:
    include:
      - main

pool:
  vmImage: ubuntu-latest
  demands: npm

resources:
  repositories:
    - repository: appsectemplates
      type: git
      name: DevSecOps/DevSecOps
      ref: 'orcaContainerScan'

variables:
  - name: scanTemplate
    ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
      value: 'pipeline_templates/Security_tasks/prepareSonarcloudPR.yml@appsectemplates'
    ${{ else }}:
      value: 'pipeline_templates/Security_tasks/prepareSonarCloud.yml@appsectemplates'

steps:
  - task: UseNode@1
    displayName: Use Node.js
    inputs:
      version: '22.x'
      checkLatest: true

  - task: Yarn@3
    displayName: Install Dependencies

  - task: Yarn@3
    displayName: Run Lint
    inputs:
      arguments: 'lint'

  - task: Yarn@3
    displayName: Run Tests
    inputs:
      arguments: 'test:cov --passWithNoTests'

  - task: PublishCodeCoverageResults@2
    inputs:
      summaryFileLocation: '$(Build.Repository.Name)/coverage/lcov.info'

  - template: ${{ variables['scanTemplate'] }}
    parameters:
      SCServiceConnection: "SonarcloudServer"
      SCProjectKey: "GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_NORTHSTAR_KPI_CATALOG_API"
      SCProjectName: "GHQ_ABI_PEOPLE_TECH_PLATFORM_GHQ_ABI_NORTHSTAR_KPI_CATALOG_API"
      SCBaseDirPath: "./$(Build.Repository.Name)"
      SCSourceEncoding: "UTF-8"
      SCReportsPathType: sonar.javascript.lcov.reportPaths
      SCReportsPath: ./coverage/lcov.info
      SCExclusion: '**/node_modules/**,**/test/**,**/tests/**,**/coverage/**,**/dist/**,**/build/**,**/*.spec.ts'
      scanOrca: false
      ${{ if or(eq(variables['Build.Reason'], 'IndividualCI'), eq(variables['Build.Reason'], 'Manual'))}}:
        SCBranchName: "$(Build.SourceBranchName)"
      ${{ if eq(variables['Build.Reason'], 'PullRequest') }}:
        SCPRKey: $(System.PullRequest.PullRequestId)
        SCPrBranch: $(System.PullRequest.SourceBranch)
        SCPrBaseBranch: $(System.PullRequest.TargetBranch)

  - task: Yarn@3
    displayName: Run Build
    inputs:
      arguments: 'build'

  - task: Yarn@3
    displayName: Publish Package
    inputs:
      arguments: 'publish'
