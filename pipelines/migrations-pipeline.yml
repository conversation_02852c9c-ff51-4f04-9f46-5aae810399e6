trigger: none

pool:
  vmImage: ubuntu-latest

parameters:
  - name: selectedEnv
    displayName: Select environment
    type: string
    default: 'development'
    values:
      - 'development'
      - 'staging'
      - 'production'

variables:
  - name: imagePullSecrets
    value: 'acr-registry'
  - ${{ if eq(parameters.selectedEnv, 'production') }}:
    - name: k8sNamespace
      value: 'tsc-prod'
    - name: k8srvcon
      value: 'peopleproducts-aks-gb-prod-tsc-prod'
    - name: dbUser
      value: db-user
    - name: dbPass
      value: db-pass
  - ${{ elseif eq(parameters.selectedEnv, 'staging') }}:
    - name: k8sNamespace
      value: 'tsc-qa'
    - name: k8srvcon
      value: 'peopleproducts-aks-gb-dev-tsc-qa'
    - name: dbUser
      value: db-user
    - name: dbPass
      value: db-pass
  - ${{ else }}:
    - name: k8sNamespace
      value: 'tsc-dev'
    - name: k8srvcon
      value: 'peopleproducts-aks-gb-dev-tsc-dev'
    - name: dbUser
      value: tsc-db-dev-user
    - name: dbPass
      value: tsc-db-dev-pass

stages:
  - stage: Migrations
    condition: eq(variables['Build.Reason'], 'Manual')
    jobs:
      - job: Run
        steps:
          - checkout: self

          - task: replacetokens@5
            inputs:
              rootDirectory: 'pipelines/manifests'
              targetFiles: '*.yml'
              tokenPattern: 'azpipelines'
              enableTelemetry: false

          - task: Docker@2
            displayName: 'Docker build and push'
            inputs:
              containerRegistry: 'AzurePeopleProducts'
              repository: northstar-domain
              command: 'buildAndPush'
              Dockerfile: '**/Dockerfile'
              tags: |
                $(Build.BuildId)
                latest

          - task: KubernetesManifest@0
            displayName: Deploy
            inputs:
                action: deploy
                connectionType: 'kubernetesServiceConnection'
                kubernetesServiceConnection: $(k8srvcon)
                namespace: $(k8sNamespace)
                imagePullSecrets: $(imagePullSecrets)
                manifests: |
                  pipelines/manifests/*.yml
                containers: |
                    peopleproductsacr.azurecr.io/northstar-domain:$(Build.BuildId)
